[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "funasr"
dynamic = ["version"]
description = "FunASR: A Fundamental End-to-End Speech Recognition Toolkit"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Speech Lab of Alibaba Group", email = "<EMAIL>"}
]
maintainers = [
    {name = "Speech Lab of Alibaba Group", email = "<EMAIL>"}
]
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Science/Research",
    "Operating System :: POSIX :: Linux",
    "License :: OSI Approved :: MIT License",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
keywords = ["speech", "recognition", "ASR", "TTS", "speech synthesis"]

# Core dependencies (from setup.py install requirements)
dependencies = [
    "scipy>=1.4.1",
    "librosa",
    "jamo",
    "PyYAML>=5.1.2",
    "soundfile>=0.12.1",
    "kaldiio>=2.17.0",
    "torch_complex",
    "sentencepiece",
    "jieba",
    "pytorch_wpe",
    "editdistance>=0.5.2",
    "oss2",
    "tqdm",
    "umap_learn",
    "jaconv",
    "hydra-core>=1.3.2",
    "tensorboardX",
    "requests",
    "modelscope",
    "torch>=2.5.1",
    "torchaudio>=2.5.1",
    "pydub>=0.25.1",
    "dynaconf>=3.2.11",
    "celery>=5.5.3",
    "redis>=5.0.0",
    "sskw-tasks>=0.4.1",
]

[project.optional-dependencies]
# Training dependencies
train = [
    "editdistance",
]

# All optional dependencies (heavy ML packages)
all = [
    "torch_optimizer",
    "fairscale", 
    "transformers",
    "openai-whisper",
]

# Testing dependencies
test = [
    "pytest>=3.3.0",
    "pytest-timeouts>=1.2.1",
    "pytest-pythonpath>=0.7.3",
    "pytest-cov>=2.7.1",
    "hacking>=2.0.0",
    "mock>=2.0.0",
    "pycodestyle",
    "jsondiff<2.0.0,>=1.2.0",
    "flake8>=3.7.8",
    "flake8-docstrings>=1.3.1",
    "black",
]

# Documentation dependencies
doc = [
    "Jinja2",
    "Sphinx",
    "sphinx-rtd-theme>=0.2.4",
    "sphinx-argparse>=0.2.5",
    "commonmark",
    "recommonmark>=0.4.0",
    "nbsphinx>=0.4.2",
    "sphinx-markdown-tables>=0.0.12",
    "configargparse>=1.2.1",
]

# LLM dependencies
llm = [
    "transformers",
    "accelerate",
    "tiktoken",
    "einops",
    "transformers_stream_generator",
    "scipy",
    "torchvision",
    "pillow",
    "matplotlib",
]

# Development dependencies (combines train, test, doc)
dev = [
    "funasr[train,test,doc]",
    "pre-commit",
    "mypy",
]

[project.scripts]
funasr = "funasr.bin.inference:main_hydra"
funasr-train = "funasr.bin.train:main_hydra"
funasr-export = "funasr.bin.export:main_hydra"
scp2jsonl = "funasr.datasets.audio_datasets.scp2jsonl:main_hydra"
jsonl2scp = "funasr.datasets.audio_datasets.jsonl2scp:main_hydra"
sensevoice2jsonl = "funasr.datasets.audio_datasets.sensevoice2jsonl:main_hydra"
funasr-scp2jsonl = "funasr.datasets.audio_datasets.scp2jsonl:main_hydra"
funasr-jsonl2scp = "funasr.datasets.audio_datasets.jsonl2scp:main_hydra"
funasr-sensevoice2jsonl = "funasr.datasets.audio_datasets.sensevoice2jsonl:main_hydra"

[project.urls]
Homepage = "https://github.com/alibaba-damo-academy/FunASR"
Repository = "https://github.com/alibaba-damo-academy/FunASR.git"
Documentation = "https://funasr.readthedocs.io/"
"Bug Tracker" = "https://github.com/alibaba-damo-academy/FunASR/issues"

[tool.setuptools.dynamic]
version = {file = "funasr/version.txt"}

[tool.setuptools.packages.find]
include = ["funasr*"]

[tool.setuptools.package-data]
funasr = ["version.txt"]

# UV specific configurations
[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"
# dev-dependencies = [
#    "pytest>=7.0",
#    "black>=22.0",
#    "isort>=5.0",
#    "mypy>=1.0",
#    "pre-commit>=2.0",
#]

# Tool configurations
[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
ignore_missing_imports = true
