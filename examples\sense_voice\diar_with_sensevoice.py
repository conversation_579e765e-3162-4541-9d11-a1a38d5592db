import json
import funasr
import os
import time

# 确保音频文件存在，如果不存在则提示用户。
# 这里我们创建一个虚拟的音频文件路径作为示例。
# ** 用户需要将此路径替换为自己的真实音频文件路径 **
input_audio_file = "5_中间片段.m4a"

# 为了使脚本能够直接运行，我们检查一个示例文件是否存在。
# 如果不存在，可以提示用户下载或使用自己的文件。
if not os.path.exists(input_audio_file):
    print(f"示例音频文件 '{input_audio_file}' 不存在。")
    exit(1)


print("正在初始化模型，这可能需要一些时间，因为它会自动从ModelScope下载所需的模型...")

# 初始化 AutoModel
# - model: 指定主任务模型，这里是 SenseVoiceSmall 进行语音识别。
# - vad_model: 指定语音活动检测模型。
# - spk_model: 指定说话人识别（嵌入提取）模型。
# - punc_model: 指定标点恢复模型。当使用说话人日志时，需要该模型对文本进行切分。
# - device: 指定运行设备，"cuda:0" 表示使用第一个 GPU，"cpu" 表示使用 CPU。
#   如果您的机器上没有可用的 GPU，请将其设置为 "cpu"。

# !!! 注意: funasr 的说话人日志流水线目前与 Paraformer 系列模型（如 'iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch'）
# !!! 结合得最好，因为它们能够生成精确的时间戳，这是说话人归属的关键。
# !!! SenseVoiceSmall 虽然性能优异，但在与 spk_model 结合使用时，可能会因时间戳格式不兼容而出现问题。
# !!! 以下代码加入了 punc_model 以解决 UnboundLocalError，但若仍有问题，建议更换 ASR 模型为 Paraformer。
model = funasr.AutoModel(
    model="iic/SenseVoiceSmall",
    vad_model="fsmn-vad",
    spk_model="cam++",
    punc_model="ct-punc",
    vad_kwargs={"max_single_segment_time": 300000},
    device="cpu",  # 如果没有 GPU，请修改为 "cpu"
    disable_update=True,
    return_raw_text=False,
    return_spk_res=True,
    sentence_timestamp=True,
)

print("模型初始化完成，开始进行推理...")

# 记录转写开始时间
transcription_start = time.time()
    

# 使用 AutoModel 对输入的音频文件进行端到端的说话人日志和语音识别
# `auto_model` 内部会处理整个流水线：VAD -> 说话人嵌入 -> 聚类 -> ASR -> 结果合并
results = model.generate(
    input=input_audio_file,
    batch_size_s=60,        # VAD 处理的批次大小（秒）
    max_sv_length=15,      # 说话人识别模型处理的最大音频长度（秒）
    use_itn=True,
    output_timestamps=False,
    output_dir="./output"  # （可选）输出中间结果的目录
)
# 记录转写结束时间
transcription_time = time.time() - transcription_start
        

# 获取文件信息
file_size = os.path.getsize(input_audio_file) / (1024 * 1024)  # MB
print(f"📊 文件大小: {file_size:.2f} MB")

 # 处理结果
transcription_result = {
    "file_path": input_audio_file,
    "file_name": os.path.basename(input_audio_file),
    "file_size_mb": file_size,
    "text":  "",
    "speaker_segments": [],
    "statistics": {
        "transcription_time": transcription_time,
    }
}

if results and len(results) > 0:
    result = results[0]
    transcription_result["text"] = result.get("text", "")
    sentence_info = result.get("sentence_info", [])
    for sentence in sentence_info:
        segment = {
            "start_time": sentence.get("start", 0) / 1004.0,
            "end_time": sentence.get("end", 0) / 1000.0,
            "speaker": f"{sentence.get('spk', 0)}",
            "text": sentence.get("text", "")
        }
        transcription_result["speaker_segments"].append(segment)

# 显示结果摘要
speaker_count = len(set([s['speaker'] for s in transcription_result["speaker_segments"]]))
total_duration = max([s["end_time"] for s in transcription_result["speaker_segments"]]) if transcription_result["speaker_segments"] else 0

print(f"✅ 转写完成:")
print(f"   - 说话人数量: {speaker_count}")
print(f"   - 音频时长: {total_duration:.1f}秒")
print(f"   - 转写时间: {transcription_time:.2f}秒")

with open("result.json", "w", encoding="utf-8") as f:
    f.write(json.dumps(transcription_result, ensure_ascii=False))

