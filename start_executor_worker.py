import os
import platform
from celery import Celery

from tasks.sskw_task import sskw_audio_transcription_sensevoice_cpu_impl
from config import cfg

# os.environ['CELERY_BROKER_URL'] = 'redis://:celeryhub123@************:6389/0'
# os.environ['CELERY_RESULT_BACKEND'] = 'redis://:celeryhub123@************:6389/1'
CELERY_BROKER_URL =  cfg.CELERY.BROKER_URL
CELERY_RESULT_BACKEND = cfg.CELERY.RESULT_BACKEND

app = Celery(
    'sskw_tasks',
    broker=CELERY_BROKER_URL,
    backend=CELERY_RESULT_BACKEND,
    include=['sskw.tasks'],
    timezone='UTC',
    enable_utc=True,
)

# 自动发现任务
# app.autodiscover_tasks(['sskw.tasks'])


if __name__ == '__main__':
    # 检测操作系统
    system = platform.system().lower()
    
    # 基础启动参数
    base_args = [
        'worker', 
        '--loglevel=info',
        '--queues=meeting_v1',
        '--concurrency=1',
        '--include=sskw.tasks.audio_transcription_sensevoice',
        '--hostname=transcription.sensevoice.cpu@liweipc'
    ]
    
    # 根据操作系统添加特定参数
    if system == 'windows':
        # Windows环境：使用solo pool避免进程池问题
        platform_args = [
            '--pool=solo'
        ]
        print("🪟 检测到Windows环境，使用solo pool配置")
    else:
        # Linux/Unix环境：使用默认prefork pool获得更好性能
        platform_args = [
            '--pool=prefork'
        ]
        print("🐧 检测到Linux/Unix环境，使用prefork pool配置")
    
    # 合并参数
    final_args = base_args + platform_args
    
    print(f"📋 启动参数: {' '.join(final_args)}")
    print("🚀 启动worker，监听队列 meeting_v1 中的所有任务")
    print("💡 任务执行由发送方指定具体任务名称控制")
    print("   - 可用任务: sskw.audio.transcription.sensevoice")
    
    # 启动worker
    app.worker_main(argv=final_args)